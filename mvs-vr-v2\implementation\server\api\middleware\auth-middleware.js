/**
 * Authentication Middleware
 *
 * This middleware handles authentication for the server-side API.
 * It validates tokens from both Directus and Supabase, implements
 * token refresh, CSRF protection, and rate limiting.
 *
 * Enhanced with:
 * - Secure cookie storage for tokens
 * - Comprehensive error handling and logging
 * - Enhanced rate limiting with progressive penalties
 * - JWE (JSON Web Encryption) for token security
 * - Token revocation system
 */

import jwt from 'jsonwebtoken';
import { createClient } from '@supabase/supabase-js';
import rateLimit from 'express-rate-limit';
import crypto from 'crypto';
import cookieParser from 'cookie-parser';
import jose from 'node-jose';
import { v4 as uuidv4 } from 'uuid';
import winston from 'winston';
import Redis from 'ioredis';
import RedisStore from 'rate-limit-redis';
import { csrfProtection, csrfTokenGenerator } from './csrf-express-adapter.js';

// Configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const directusUrl = process.env.DIRECTUS_URL || 'http://localhost:8055';
const directusSecret = process.env.DIRECTUS_SECRET || 'directus-secret';
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
const nodeEnv = process.env.NODE_ENV || 'development';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize Redis client for token blacklist and rate limiting
const redis = new Redis(redisUrl);

// Initialize logger
const logger = winston.createLogger({
  level: nodeEnv === 'production' ? 'info' : 'debug',
  format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
  defaultMeta: { service: 'auth-service' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
    }),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

// Initialize JWE keystore
let keystore;
const initializeKeystore = async () => {
  try {
    // Check if keystore exists in Redis
    const keystoreJson = await redis.get('auth:keystore');

    if (keystoreJson) {
      keystore = await jose.JWK.asKeyStore(keystoreJson);
    } else {
      // Generate new keystore
      keystore = jose.JWK.createKeyStore();

      // Generate a new RSA key
      await keystore.generate('RSA', 2048, {
        alg: 'RSA-OAEP-256',
        use: 'enc',
      });

      // Store keystore in Redis
      await redis.set('auth:keystore', JSON.stringify(keystore.toJSON(true)));
    }

    logger.info('JWE keystore initialized');
  } catch (error) {
    logger.error('Failed to initialize JWE keystore:', error);
    throw error;
  }
};

// Initialize keystore
initializeKeystore().catch(error => {
  logger.error('Failed to initialize keystore:', error);
});

// Create Redis store for rate limiter

// Create progressive rate limiter
const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    store: new RedisStore({
      // @ts-ignore - Type definitions are incorrect
      sendCommand: (...args) => redis.call(...args),
      prefix: 'rate-limit:',
    }),
    keyGenerator: req => {
      // Use IP and route for rate limiting
      return `${req.ip}:${req.originalUrl}`;
    },
    skip: req => {
      // Skip rate limiting for trusted IPs
      const trustedIps = process.env.TRUSTED_IPS ? process.env.TRUSTED_IPS.split(',') : [];
      return trustedIps.includes(req.ip);
    },
    handler: (req, res) => {
      // Log rate limit exceeded
      logger.warn(`Rate limit exceeded for ${req.ip} on ${req.originalUrl}`);

      // Track failed attempts for progressive penalties
      trackFailedAttempt(req.ip);

      // Return error response
      res.status(429).json({
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests, please try again later.',
          retryAfter: Math.ceil(req.rateLimit.resetTime / 1000) - Math.floor(Date.now() / 1000),
        },
      });
    },
  };

  return rateLimit({
    ...defaultOptions,
    ...options,
  });
};

// Track failed attempts for progressive penalties
const trackFailedAttempt = async ip => {
  try {
    // Increment failed attempts counter
    const key = `failed-attempts:${ip}`;
    const attempts = await redis.incr(key);

    // Set expiry if not already set
    if (attempts === 1) {
      await redis.expire(key, 24 * 60 * 60); // 24 hours
    }

    // Apply progressive penalties
    if (attempts >= 10) {
      // Block IP for 24 hours after 10 failed attempts
      await redis.set(`blocked:${ip}`, 'true', 'EX', 24 * 60 * 60);
      logger.warn(`IP ${ip} blocked for 24 hours due to excessive failed attempts`);
    } else if (attempts >= 5) {
      // Block IP for 1 hour after 5 failed attempts
      await redis.set(`blocked:${ip}`, 'true', 'EX', 60 * 60);
      logger.warn(`IP ${ip} blocked for 1 hour due to excessive failed attempts`);
    }
  } catch (error) {
    logger.error('Error tracking failed attempt:', error);
  }
};

// Check if IP is blocked
const isIpBlocked = async ip => {
  try {
    return (await redis.exists(`blocked:${ip}`)) === 1;
  } catch (error) {
    logger.error('Error checking if IP is blocked:', error);
    return false;
  }
};

// Create different rate limiters for different endpoints
const apiLimiter = createRateLimiter();
const authLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 requests per hour for auth endpoints
  message: {
    status: 429,
    error: 'Too many authentication attempts, please try again later.',
  },
});

// Create CSRF protection using iron-session
// Note: The csrfProtection is imported from csrf-express-adapter.ts

/**
 * Encrypt a token using JWE
 * @param {string} token - Token to encrypt
 * @returns {Promise<string>} - Encrypted token
 */
const encryptToken = async token => {
  try {
    // Get encryption key
    const key = keystore.get({ use: 'enc' });

    if (!key) {
      throw new Error('Encryption key not found');
    }

    // Encrypt token
    const result = await jose.JWE.createEncrypt({ format: 'compact' }).update(token).encrypt(key);

    return result;
  } catch (error) {
    logger.error('Failed to encrypt token:', error);
    throw new Error(`Failed to encrypt token: ${error.message}`);
  }
};

/**
 * Decrypt a token using JWE
 * @param {string} encryptedToken - Encrypted token
 * @returns {Promise<string>} - Decrypted token
 */
const decryptToken = async encryptedToken => {
  try {
    // Decrypt token
    const result = await jose.JWE.createDecrypt(keystore).decrypt(encryptedToken);

    return result.plaintext.toString();
  } catch (error) {
    logger.error('Failed to decrypt token:', error);
    throw new Error(`Failed to decrypt token: ${error.message}`);
  }
};

/**
 * Check if a token is revoked
 * @param {string} token - Token to check
 * @returns {Promise<boolean>} - True if token is revoked
 */
const isTokenRevoked = async token => {
  try {
    // Generate token hash
    const hash = crypto.createHash('sha256').update(token).digest('hex');

    // Check if token is in blacklist
    return (await redis.exists(`revoked:${hash}`)) === 1;
  } catch (error) {
    logger.error('Error checking if token is revoked:', error);
    return false;
  }
};

/**
 * Revoke a token
 * @param {string} token - Token to revoke
 * @param {number} expiresIn - Seconds until token expiration
 * @returns {Promise<void>}
 */
const revokeToken = async (token, expiresIn = 86400) => {
  try {
    // Generate token hash
    const hash = crypto.createHash('sha256').update(token).digest('hex');

    // Add token to blacklist
    await redis.set(`revoked:${hash}`, 'true', 'EX', expiresIn);

    logger.info(`Token revoked: ${hash.substring(0, 8)}...`);
  } catch (error) {
    logger.error('Error revoking token:', error);
    throw new Error(`Failed to revoke token: ${error.message}`);
  }
};

/**
 * Validate a Directus token
 * @param {string} token - Directus JWT token
 * @returns {Promise<Object>} - Decoded token payload
 */
const validateDirectusToken = async token => {
  try {
    // Check if token is revoked
    if (await isTokenRevoked(token)) {
      throw new Error('Token has been revoked');
    }

    // Verify the token
    const decoded = jwt.verify(token, directusSecret);

    // Check if token is expired
    if (decoded.exp && decoded.exp < Math.floor(Date.now() / 1000)) {
      throw new Error('Token expired');
    }

    // Log successful validation
    logger.debug(`Directus token validated for user: ${decoded.id}`);

    return decoded;
  } catch (error) {
    logger.warn(`Directus token validation failed: ${error.message}`);
    throw new Error(`Invalid Directus token: ${error.message}`);
  }
};

/**
 * Validate a Supabase token
 * @param {string} token - Supabase JWT token
 * @returns {Promise<Object>} - User data
 */
const validateSupabaseToken = async token => {
  try {
    // Check if token is revoked
    if (await isTokenRevoked(token)) {
      throw new Error('Token has been revoked');
    }

    // Verify the token with Supabase
    const { data, error } = await supabase.auth.getUser(token);

    if (error) {
      throw new Error(error.message);
    }

    // Log successful validation
    logger.debug(`Supabase token validated for user: ${data.user.id}`);

    return data.user;
  } catch (error) {
    logger.warn(`Supabase token validation failed: ${error.message}`);
    throw new Error(`Invalid Supabase token: ${error.message}`);
  }
};

/**
 * Refresh a Supabase token
 * @param {string} refreshToken - Supabase refresh token
 * @returns {Promise<Object>} - New tokens
 */
const refreshSupabaseToken = async refreshToken => {
  try {
    // Check if refresh token is revoked
    if (await isTokenRevoked(refreshToken)) {
      throw new Error('Refresh token has been revoked');
    }

    // Refresh the token
    const { data, error } = await supabase.auth.refreshSession({
      refresh_token: refreshToken,
    });

    if (error) {
      throw new Error(error.message);
    }

    // Revoke old refresh token
    await revokeToken(refreshToken, 86400); // 24 hours

    // Log successful refresh
    logger.info(`Token refreshed for user: ${data.user.id}`);

    return {
      access_token: data.session.access_token,
      refresh_token: data.session.refresh_token,
      expires_at: data.session.expires_at,
    };
  } catch (error) {
    logger.error(`Failed to refresh token: ${error.message}`);
    throw new Error(`Failed to refresh token: ${error.message}`);
  }
};

/**
 * Set secure cookie with token
 * @param {Object} res - Express response object
 * @param {string} name - Cookie name
 * @param {string} value - Cookie value
 * @param {Object} options - Cookie options
 */
const setSecureCookie = (res, name, value, options = {}) => {
  const defaultOptions = {
    httpOnly: true,
    secure: nodeEnv === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    path: '/',
  };

  res.cookie(name, value, { ...defaultOptions, ...options });
  logger.debug(`Set secure cookie: ${name}`);
};

/**
 * Get token from request
 * @param {Object} req - Express request object
 * @returns {Object} - Token object with source
 */
const getTokenFromRequest = req => {
  // Check for token in cookies first (most secure)
  if (req.cookies && req.cookies.refresh_token) {
    return {
      token: req.cookies.refresh_token,
      source: 'cookie',
      type: 'refresh',
    };
  }

  // Check for token in Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return {
      token: authHeader.substring(7),
      source: 'header',
      type: 'access',
    };
  }

  // Check for token in query string (least secure, only for specific cases)
  if (req.query && req.query.token) {
    return {
      token: req.query.token,
      source: 'query',
      type: 'access',
    };
  }

  return { token: null, source: null, type: null };
};

/**
 * Track authentication event
 * @param {string} event - Event type
 * @param {Object} data - Event data
 */
const trackAuthEvent = async (event, data) => {
  try {
    const eventData = {
      timestamp: new Date().toISOString(),
      event,
      ...data,
    };

    // Log event
    logger.info(`Auth event: ${event}`, eventData);

    // Store event in Redis for analytics
    await redis.lpush('auth:events', JSON.stringify(eventData));
    await redis.ltrim('auth:events', 0, 9999); // Keep last 10000 events

    // Store in user history if user ID is provided
    if (data.userId) {
      await redis.lpush(`auth:user:${data.userId}:events`, JSON.stringify(eventData));
      await redis.ltrim(`auth:user:${data.userId}:events`, 0, 99); // Keep last 100 events per user
    }
  } catch (error) {
    logger.error('Error tracking auth event:', error);
  }
};

/**
 * Generate a new CSRF token
 * @returns {string} - CSRF token
 */
const generateCsrfToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * Authentication middleware
 * @param {Object} options - Options
 * @param {boolean} options.required - Whether authentication is required
 * @param {string[]} options.roles - Allowed roles
 * @param {boolean} options.refreshToken - Whether to refresh token if expired
 * @returns {Function} - Express middleware
 */
const authenticate = (options = { required: true, roles: [], refreshToken: true }) => {
  return async (req, res, next) => {
    try {
      // Check if IP is blocked
      const clientIp = req.ip || req.connection.remoteAddress;
      if (await isIpBlocked(clientIp)) {
        trackAuthEvent('ip_blocked', { ip: clientIp, path: req.path });
        return res.status(403).json({
          success: false,
          error: {
            code: 'IP_BLOCKED',
            message: 'Your IP address has been temporarily blocked due to suspicious activity',
          },
        });
      }

      // Get token from request
      const { token, source, type } = getTokenFromRequest(req);

      if (!token) {
        if (options.required) {
          trackAuthEvent('auth_missing_token', { ip: clientIp, path: req.path });
          return res.status(401).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'Authentication token is required',
            },
          });
        } else {
          // Continue without authentication if not required
          return next();
        }
      }

      // Try to validate as Supabase token first
      try {
        // Check if token is a refresh token
        if (type === 'refresh' && options.refreshToken) {
          // Refresh the token
          const tokens = await refreshSupabaseToken(token);

          // Set new tokens in cookies
          setSecureCookie(res, 'refresh_token', tokens.refresh_token, {
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
          });

          // Set access token in memory-only cookie (not accessible via JavaScript)
          setSecureCookie(res, 'access_token', tokens.access_token, {
            maxAge: 60 * 60 * 1000, // 1 hour
          });

          // Get user data
          const user = await validateSupabaseToken(tokens.access_token);
          req.user = user;
          req.tokens = tokens;

          trackAuthEvent('token_refreshed', {
            userId: user.id,
            ip: clientIp,
            path: req.path,
          });
        } else {
          // Validate access token
          const user = await validateSupabaseToken(token);
          req.user = user;

          trackAuthEvent('auth_success', {
            userId: user.id,
            ip: clientIp,
            path: req.path,
            tokenSource: source,
          });
        }

        // Check roles if specified
        if (options.roles && options.roles.length > 0) {
          const userRole = req.user.app_metadata?.role || 'user';

          if (!options.roles.includes(userRole)) {
            trackAuthEvent('auth_forbidden', {
              userId: req.user.id,
              ip: clientIp,
              path: req.path,
              role: userRole,
              requiredRoles: options.roles,
            });

            return res.status(403).json({
              success: false,
              error: {
                code: 'FORBIDDEN',
                message: 'User does not have the required role',
              },
            });
          }
        }

        return next();
      } catch (supabaseError) {
        // If not a valid Supabase token, try Directus
        try {
          const decoded = await validateDirectusToken(token);
          req.user = {
            id: decoded.id,
            role: decoded.role,
            directus: true,
          };

          trackAuthEvent('auth_success_directus', {
            userId: decoded.id,
            ip: clientIp,
            path: req.path,
            tokenSource: source,
          });

          // Check roles if specified
          if (options.roles && options.roles.length > 0) {
            if (!options.roles.includes(decoded.role)) {
              trackAuthEvent('auth_forbidden_directus', {
                userId: decoded.id,
                ip: clientIp,
                path: req.path,
                role: decoded.role,
                requiredRoles: options.roles,
              });

              return res.status(403).json({
                success: false,
                error: {
                  code: 'FORBIDDEN',
                  message: 'User does not have the required role',
                },
              });
            }
          }

          return next();
        } catch (directusError) {
          // Neither Supabase nor Directus token is valid
          trackAuthEvent('auth_invalid_token', {
            ip: clientIp,
            path: req.path,
            tokenSource: source,
            error: directusError.message,
          });

          // Track failed attempt for progressive penalties
          await trackFailedAttempt(clientIp);

          return res.status(401).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'Invalid authentication token',
            },
          });
        }
      }
    } catch (error) {
      logger.error('Authentication error:', error);

      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An error occurred while authenticating the request',
        },
      });
    }
  };
};

export {
  // Authentication middleware
  authenticate,

  // Rate limiting
  apiLimiter,
  authLimiter,

  // CSRF protection
  csrfProtection,
  generateCsrfToken,

  // Token management
  validateDirectusToken,
  validateSupabaseToken,
  refreshSupabaseToken,
  encryptToken,
  decryptToken,
  revokeToken,
  isTokenRevoked,

  // Cookie management
  setSecureCookie,
  getTokenFromRequest,

  // Security tracking
  trackAuthEvent,
  trackFailedAttempt,
  isIpBlocked,

  // Utilities
  logger,
  redis,
};
