/**
 * Unit tests for the authentication middleware
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import jwt from 'jsonwebtoken';
import { createClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import winston from 'winston';
import process from 'node:process';

// Mock dependencies
vi.mock('jsonwebtoken', () => ({
  default: {
    verify: vi.fn(),
    sign: vi.fn(),
  },
}));

vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn(),
}));

vi.mock('ioredis', () => ({
  default: vi.fn(),
}));

vi.mock('winston', () => ({
  default: {
    createLogger: vi.fn(),
    format: {
      combine: vi.fn(() => vi.fn()),
      timestamp: vi.fn(() => vi.fn()),
      errors: vi.fn(() => vi.fn()),
      json: vi.fn(() => vi.fn()),
      colorize: vi.fn(() => vi.fn()),
      simple: vi.fn(() => vi.fn()),
    },
    transports: {
      Console: vi.fn(),
      File: vi.fn(),
    },
  },
}));

vi.mock('../../utils/logger.js', () => ({
  getLogger: vi.fn(),
}));

// Mock Redis client
const mockRedis = {
  get: vi.fn(),
  set: vi.fn(),
  exists: vi.fn(),
  incr: vi.fn(),
  expire: vi.fn(),
  lpush: vi.fn(),
  ltrim: vi.fn(),
  call: vi.fn(),
};

// Mock Supabase client
const mockSupabase = {
  auth: {
    getUser: vi.fn(),
    refreshSession: vi.fn(),
    mfa: {
      enroll: vi.fn(),
      challenge: vi.fn(),
    },
  },
};

// Mock Winston logger
const mockLogger = {
  info: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
};

// Mock environment variables
process.env.DIRECTUS_SECRET = 'test-secret';
process.env.REDIS_URL = 'redis://localhost:6379';
process.env.NODE_ENV = 'test';

// Setup mocks before importing the module
const mockJwt = await import('jsonwebtoken');
const mockSupabaseJs = await import('@supabase/supabase-js');
const mockIoredis = await import('ioredis');
const mockWinston = await import('winston');
const mockLoggerModule = await import('../../utils/logger.js');

vi.mocked(mockJwt.default.verify).mockImplementation(vi.fn());
vi.mocked(mockSupabaseJs.createClient).mockReturnValue(mockSupabase);
vi.mocked(mockIoredis.default).mockImplementation(() => mockRedis);
vi.mocked(mockWinston.default.createLogger).mockReturnValue(mockLogger);
vi.mocked(mockLoggerModule.getLogger).mockReturnValue(mockLogger);

// Import the module under test
import {
  validateDirectusToken,
  validateSupabaseToken,
  refreshSupabaseToken,
  // The following imports are not used in these tests but are part of the module
  // authenticate,
  // isTokenRevoked,
  // revokeToken,
  // encryptToken,
  // decryptToken,
  // trackAuthEvent,
  // isIpBlocked,
  // trackFailedAttempt,
} from '../../api/middleware/auth-middleware.js';

describe('Auth Middleware', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
  });

  describe('validateDirectusToken', () => {
    it('should validate a valid Directus token', async () => {
      // Mock JWT verify to return a valid payload
      const mockPayload = {
        id: 'user-123',
        role: 'admin',
        exp: Math.floor(Date.now() / 1000) + 3600,
      };
      mockJwt.default.verify.mockReturnValue(mockPayload);

      // Mock token not revoked
      mockRedis.exists.mockResolvedValue(0);

      const result = await validateDirectusToken('valid-token');

      expect(mockJwt.default.verify).toHaveBeenCalledWith('valid-token', 'test-secret');
      expect(result).toEqual(mockPayload);
      expect(mockLogger.debug).toHaveBeenCalled();
    });

    it('should reject an expired Directus token', async () => {
      // Mock JWT verify to return an expired payload
      const mockPayload = {
        id: 'user-123',
        role: 'admin',
        exp: Math.floor(Date.now() / 1000) - 3600,
      };
      mockJwt.default.verify.mockReturnValue(mockPayload);

      // Mock token not revoked
      mockRedis.exists.mockResolvedValue(0);

      await expect(validateDirectusToken('expired-token')).rejects.toThrow('Token expired');
      expect(mockLogger.warn).toHaveBeenCalled();
    });

    it('should reject a revoked Directus token', async () => {
      // Mock token revoked
      mockRedis.exists.mockResolvedValue(1);

      await expect(validateDirectusToken('revoked-token')).rejects.toThrow(
        'Token has been revoked',
      );
      expect(mockLogger.warn).toHaveBeenCalled();
    });
  });

  describe('validateSupabaseToken', () => {
    it('should validate a valid Supabase token', async () => {
      // Mock Supabase getUser to return a valid user
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        app_metadata: { role: 'admin' },
      };
      mockSupabase.auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });

      // Mock token not revoked
      mockRedis.exists.mockResolvedValue(0);

      const result = await validateSupabaseToken('valid-token');

      expect(mockSupabase.auth.getUser).toHaveBeenCalledWith('valid-token');
      expect(result).toEqual(mockUser);
      expect(mockLogger.debug).toHaveBeenCalled();
    });

    it('should reject an invalid Supabase token', async () => {
      // Mock Supabase getUser to return an error
      mockSupabase.auth.getUser.mockResolvedValue({
        data: null,
        error: { message: 'Invalid token' },
      });

      // Mock token not revoked
      mockRedis.exists.mockResolvedValue(0);

      await expect(validateSupabaseToken('invalid-token')).rejects.toThrow('Invalid token');
      expect(mockLogger.warn).toHaveBeenCalled();
    });

    it('should reject a revoked Supabase token', async () => {
      // Mock token revoked
      mockRedis.exists.mockResolvedValue(1);

      await expect(validateSupabaseToken('revoked-token')).rejects.toThrow(
        'Token has been revoked',
      );
      expect(mockLogger.warn).toHaveBeenCalled();
    });
  });

  describe('refreshSupabaseToken', () => {
    it('should refresh a valid Supabase token', async () => {
      // Mock Supabase refreshSession to return new tokens
      const mockSession = {
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        expires_at: new Date(Date.now() + 3600000).toISOString(),
      };
      mockSupabase.auth.refreshSession.mockResolvedValue({
        data: { session: mockSession, user: { id: 'user-123' } },
        error: null,
      });

      // Mock token not revoked
      mockRedis.exists.mockResolvedValue(0);

      const result = await refreshSupabaseToken('valid-refresh-token');

      expect(mockSupabase.auth.refreshSession).toHaveBeenCalledWith({
        refresh_token: 'valid-refresh-token',
      });
      expect(result).toEqual({
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        expires_at: mockSession.expires_at,
      });
      expect(mockLogger.info).toHaveBeenCalled();
    });

    it('should reject an invalid refresh token', async () => {
      // Mock Supabase refreshSession to return an error
      mockSupabase.auth.refreshSession.mockResolvedValue({
        data: null,
        error: { message: 'Invalid refresh token' },
      });

      // Mock token not revoked
      mockRedis.exists.mockResolvedValue(0);

      await expect(refreshSupabaseToken('invalid-refresh-token')).rejects.toThrow(
        'Invalid refresh token',
      );
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should reject a revoked refresh token', async () => {
      // Mock token revoked
      mockRedis.exists.mockResolvedValue(1);

      await expect(refreshSupabaseToken('revoked-refresh-token')).rejects.toThrow(
        'Refresh token has been revoked',
      );
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  // Add more tests for other functions...
});
